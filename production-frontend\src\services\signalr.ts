/**
 * SignalR Service
 * Real-time communication service using SignalR
 */

import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr'

export enum HubMethod {
  // Document collaboration
  JOIN_DOCUMENT_GROUP = 'JoinDocumentGroup',
  LEAVE_DOCUMENT_GROUP = 'LeaveDocumentGroup',
  SEND_CURSOR_POSITION = 'SendCursorPosition',
  SEND_DOCUMENT_CHANGE = 'SendDocumentChange',

  // Document comments
  DOCUMENT_COMMENT_ADDED = 'DocumentCommentAdded',
  DOCUMENT_COMMENT_UPDATED = 'DocumentCommentUpdated',
  DOCUMENT_COMMENT_DELETED = 'DocumentCommentDeleted',

  // Document versions
  DOCUMENT_VERSION_CREATED = 'DocumentVersionCreated',

  // Notifications
  SEND_NOTIFICATION = 'SendNotification',
  JOIN_USER_GROUP = 'JoinUserGroup',
  LEAVE_USER_GROUP = 'LeaveUserGroup',

  // Project collaboration
  JOIN_PROJECT_GROUP = 'JoinProjectGroup',
  LEAVE_PROJECT_GROUP = 'LeaveProjectGroup',

  // System events
  SYSTEM_ALERT = 'SystemAlert',
  HEALTH_UPDATE = 'HealthUpdate',
}

export enum HubEvent {
  // Document events
  DOCUMENT_CHANGED = 'DocumentChanged',
  CURSOR_MOVED = 'CursorMoved',
  USER_JOINED_DOCUMENT = 'UserJoinedDocument',
  USER_LEFT_DOCUMENT = 'UserLeftDocument',
  
  // Notification events
  NOTIFICATION_RECEIVED = 'NotificationReceived',
  
  // Project events
  PROJECT_UPDATED = 'ProjectUpdated',
  MEMBER_JOINED_PROJECT = 'MemberJoinedProject',
  MEMBER_LEFT_PROJECT = 'MemberLeftProject',
  
  // System events
  SYSTEM_ALERT_RECEIVED = 'SystemAlertReceived',
  HEALTH_STATUS_CHANGED = 'HealthStatusChanged',
}

export interface SignalRConfig {
  url: string
  accessTokenFactory?: () => string | Promise<string>
  automaticReconnect?: boolean
  logLevel?: LogLevel
}

export interface DocumentChange {
  documentId: string
  userId: string
  userName: string
  change: {
    type: 'insert' | 'delete' | 'replace'
    position: number
    content: string
    length?: number
  }
  timestamp: string
}

export interface CursorPosition {
  documentId: string
  userId: string
  userName: string
  avatarUrl?: string
  position: number
  selection?: {
    start: number
    end: number
  }
  timestamp: string
}

export interface UserPresence {
  userId: string
  userName: string
  avatarUrl?: string
  status: 'online' | 'away' | 'offline'
  lastSeen: string
}

class SignalRService {
  private connection: HubConnection | null = null
  private config: SignalRConfig
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000

  constructor(config: SignalRConfig) {
    this.config = config
  }

  async connect(): Promise<void> {
    if (this.connection?.state === 'Connected') {
      return
    }

    try {
      this.connection = new HubConnectionBuilder()
        .withUrl(this.config.url, {
          accessTokenFactory: this.config.accessTokenFactory,
        })
        .withAutomaticReconnect({
          nextRetryDelayInMilliseconds: (retryContext) => {
            if (retryContext.previousRetryCount < this.maxReconnectAttempts) {
              return this.reconnectDelay * Math.pow(2, retryContext.previousRetryCount)
            }
            return null // Stop reconnecting
          }
        })
        .configureLogging(this.config.logLevel || LogLevel.Information)
        .build()

      // Set up event handlers
      this.setupEventHandlers()

      await this.connection.start()
      console.log('SignalR connected successfully')
      this.reconnectAttempts = 0
    } catch (error) {
      console.error('SignalR connection failed:', error)
      this.handleConnectionError(error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    if (this.connection) {
      await this.connection.stop()
      this.connection = null
      console.log('SignalR disconnected')
    }
  }

  async invoke(methodName: string, ...args: any[]): Promise<any> {
    if (!this.connection || this.connection.state !== 'Connected') {
      throw new Error('SignalR connection is not established')
    }

    try {
      return await this.connection.invoke(methodName, ...args)
    } catch (error) {
      console.error(`SignalR invoke failed for method ${methodName}:`, error)
      throw error
    }
  }

  on(eventName: string, callback: (...args: any[]) => void): void {
    if (!this.connection) {
      throw new Error('SignalR connection is not established')
    }

    this.connection.on(eventName, callback)
  }

  off(eventName: string, callback?: (...args: any[]) => void): void {
    if (!this.connection) {
      return
    }

    if (callback) {
      this.connection.off(eventName, callback)
    } else {
      this.connection.off(eventName)
    }
  }

  getConnectionState(): string {
    return this.connection?.state || 'Disconnected'
  }

  private setupEventHandlers(): void {
    if (!this.connection) return

    this.connection.onreconnecting((error) => {
      console.warn('SignalR reconnecting:', error)
    })

    this.connection.onreconnected((connectionId) => {
      console.log('SignalR reconnected:', connectionId)
      this.reconnectAttempts = 0
    })

    this.connection.onclose((error) => {
      console.log('SignalR connection closed:', error)
      if (error) {
        this.handleConnectionError(error)
      }
    })
  }

  private handleConnectionError(error: any): void {
    this.reconnectAttempts++
    console.error(`SignalR connection error (attempt ${this.reconnectAttempts}):`, error)

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached. Giving up.')
      // Emit a custom event or call a callback to notify the application
      window.dispatchEvent(new CustomEvent('signalr-connection-failed', { detail: error }))
    }
  }

  // Document collaboration methods
  async joinDocumentGroup(documentId: string, userId: string, userName: string, avatarUrl?: string): Promise<void> {
    return this.invoke(HubMethod.JOIN_DOCUMENT_GROUP, documentId, userId, userName, avatarUrl)
  }

  async leaveDocumentGroup(documentId: string, userId: string): Promise<void> {
    return this.invoke(HubMethod.LEAVE_DOCUMENT_GROUP, documentId, userId)
  }

  async sendCursorPosition(documentId: string, userId: string, userName: string, path: string, offset: number): Promise<void> {
    return this.invoke(HubMethod.SEND_CURSOR_POSITION, documentId, userId, userName, path, offset)
  }

  async sendDocumentChange(change: DocumentChange): Promise<void> {
    return this.invoke(HubMethod.SEND_DOCUMENT_CHANGE, change)
  }

  // Notification methods
  async joinUserGroup(userId: string): Promise<void> {
    return this.invoke(HubMethod.JOIN_USER_GROUP, userId)
  }

  async leaveUserGroup(userId: string): Promise<void> {
    return this.invoke(HubMethod.LEAVE_USER_GROUP, userId)
  }

  // Project collaboration methods
  async joinProjectGroup(projectId: string, userId: string): Promise<void> {
    return this.invoke(HubMethod.JOIN_PROJECT_GROUP, projectId, userId)
  }

  async leaveProjectGroup(projectId: string, userId: string): Promise<void> {
    return this.invoke(HubMethod.LEAVE_PROJECT_GROUP, projectId, userId)
  }
}

// Create and export a singleton instance
const signalRConfig: SignalRConfig = {
  url: `${process.env.NEXT_PUBLIC_API_URL || 'https://hepzlogic.azurewebsites.net'}/signalr`,
  accessTokenFactory: () => {
    // Get token from your auth system
    const token = localStorage.getItem('accessToken')
    return token || ''
  },
  automaticReconnect: true,
  logLevel: process.env.NODE_ENV === 'development' ? LogLevel.Debug : LogLevel.Warning,
}

export const signalRService = new SignalRService(signalRConfig)

// Export types and enums (remove duplicate exports)
export type {
  SignalRConfig as SignalRServiceConfig,
  DocumentChange as SignalRDocumentChange,
  CursorPosition as SignalRCursorPosition,
  UserPresence as SignalRUserPresence
}
