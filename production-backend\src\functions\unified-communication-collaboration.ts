/**
 * Unified Communication & Collaboration Function
 * Consolidates all communication operations: real-time collaboration, messaging, push notifications,
 * email service, and email automation
 * Replaces: real-time-collaboration.ts, real-time-messaging.ts, push-notifications.ts,
 *          email-service.ts, email-automation.ts
 * 
 * Production-ready implementation with comprehensive error handling,
 * full Service Bus, Event Hub, Redis integrations, and enterprise-grade communication
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from 'uuid';
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { eventGridIntegration } from '../shared/services/event-grid-integration';
import { ServiceBusEnhancedService } from '../shared/services/service-bus';
import { signalRService } from '../shared/services/signalr';

// Unified communication types and enums
enum CommunicationType {
  COLLABORATION = 'COLLABORATION',
  MESSAGING = 'MESSAGING',
  PUSH_NOTIFICATION = 'PUSH_NOTIFICATION',
  EMAIL = 'EMAIL',
  EMAIL_AUTOMATION = 'EMAIL_AUTOMATION'
}

enum SessionStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ENDED = 'ENDED'
}

enum MessageType {
  TEXT = 'TEXT',
  FILE = 'FILE',
  IMAGE = 'IMAGE',
  SYSTEM = 'SYSTEM',
  OPERATION = 'OPERATION'
}

enum NotificationPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

enum DeliveryStatus {
  PENDING = 'PENDING',
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

enum EmailStatus {
  DRAFT = 'DRAFT',
  QUEUED = 'QUEUED',
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  FAILED = 'FAILED',
  BOUNCED = 'BOUNCED'
}

enum EmailType {
  WELCOME = 'WELCOME',
  NOTIFICATION = 'NOTIFICATION',
  REMINDER = 'REMINDER',
  INVITATION = 'INVITATION',
  REPORT = 'REPORT',
  MARKETING = 'MARKETING',
  TRANSACTIONAL = 'TRANSACTIONAL',
  SYSTEM = 'SYSTEM'
}

enum NotificationPlatform {
  IOS = 'IOS',
  ANDROID = 'ANDROID',
  WEB = 'WEB',
  WINDOWS = 'WINDOWS'
}

// Comprehensive interfaces
interface CollaborationSession {
  id: string;
  documentId: string;
  organizationId: string;
  projectId?: string;
  name: string;
  description?: string;
  status: SessionStatus;
  participants: SessionParticipant[];
  maxParticipants: number;
  settings: SessionSettings;
  metadata: { [key: string]: any };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  endedAt?: string;
  tenantId: string;
}

interface SessionParticipant {
  userId: string;
  role: string;
  permissions: string[];
  joinedAt: string;
  lastActivity: string;
  cursor?: CursorPosition;
  isActive: boolean;
}

interface SessionSettings {
  allowAnonymous: boolean;
  requireApproval: boolean;
  enableVoiceChat: boolean;
  enableVideoChat: boolean;
  enableScreenShare: boolean;
  autoSave: boolean;
  conflictResolution: string;
}

interface CursorPosition {
  line: number;
  column: number;
  selection?: {
    start: { line: number; column: number };
    end: { line: number; column: number };
  };
}

interface Message {
  id: string;
  channelId?: string;
  sessionId?: string;
  type: MessageType;
  content: string;
  metadata: { [key: string]: any };
  attachments: MessageAttachment[];
  senderId: string;
  recipientIds: string[];
  organizationId: string;
  projectId?: string;
  parentMessageId?: string;
  threadId?: string;
  reactions: MessageReaction[];
  editHistory: MessageEdit[];
  deliveryStatus: DeliveryStatus;
  readBy: MessageRead[];
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  tenantId: string;
}

interface MessageAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  metadata: { [key: string]: any };
}

interface MessageReaction {
  emoji: string;
  userId: string;
  timestamp: string;
}

interface MessageEdit {
  content: string;
  editedBy: string;
  editedAt: string;
}

interface MessageRead {
  userId: string;
  readAt: string;
}

interface PushNotification {
  id: string;
  organizationId: string;
  notification: NotificationContent;
  recipients: NotificationRecipient[];
  options: NotificationOptions;
  scheduling?: NotificationScheduling;
  status: DeliveryStatus;
  deliveryStats: DeliveryStats;
  createdBy: string;
  createdAt: string;
  sentAt?: string;
  tenantId: string;
}

interface NotificationContent {
  title: string;
  body: string;
  icon?: string;
  image?: string;
  badge?: number;
  sound?: string;
  data?: { [key: string]: any };
}

interface NotificationRecipient {
  userId: string;
  deviceTokens?: string[];
  tags?: string[];
  preferences?: NotificationPreferences;
}

interface NotificationPreferences {
  enabled: boolean;
  quietHours?: {
    start: string;
    end: string;
    timezone: string;
  };
  categories: { [category: string]: boolean };
}

interface NotificationOptions {
  priority: NotificationPriority;
  ttl: number;
  delayWhileIdle: boolean;
  collapseKey?: string;
  restrictedPackageName?: string;
  dryRun?: boolean;
}

interface NotificationScheduling {
  sendAt: string;
  timezone: string;
  recurring?: {
    frequency: string;
    interval: number;
    endDate?: string;
  };
}

interface DeliveryStats {
  totalRecipients: number;
  sent: number;
  delivered: number;
  failed: number;
  clicked: number;
  dismissed: number;
}

interface EmailMessage {
  id: string;
  organizationId: string;
  projectId?: string;
  type: EmailType;
  from: EmailAddress;
  to: EmailAddress[];
  cc?: EmailAddress[];
  bcc?: EmailAddress[];
  subject: string;
  content: EmailContent;
  attachments: EmailAttachment[];
  templateId?: string;
  templateData?: { [key: string]: any };
  priority: NotificationPriority;
  status: EmailStatus;
  scheduling?: EmailScheduling;
  tracking: EmailTracking;
  metadata: { [key: string]: any };
  createdBy: string;
  createdAt: string;
  sentAt?: string;
  deliveredAt?: string;
  tenantId: string;
}

interface EmailAddress {
  email: string;
  name?: string;
}

interface EmailContent {
  html?: string;
  text?: string;
  amp?: string;
}

interface EmailAttachment {
  id: string;
  name: string;
  contentType: string;
  size: number;
  content: string; // Base64 encoded
  inline: boolean;
  contentId?: string;
}

interface EmailScheduling {
  sendAt: string;
  timezone: string;
  recurring?: {
    frequency: string;
    interval: number;
    endDate?: string;
  };
}

interface EmailTracking {
  trackOpens: boolean;
  trackClicks: boolean;
  trackUnsubscribes: boolean;
  opens: EmailOpen[];
  clicks: EmailClick[];
  unsubscribes: EmailUnsubscribe[];
}

interface EmailOpen {
  timestamp: string;
  userAgent: string;
  ipAddress: string;
  location?: string;
}

interface EmailClick {
  timestamp: string;
  url: string;
  userAgent: string;
  ipAddress: string;
  location?: string;
}

interface EmailUnsubscribe {
  timestamp: string;
  reason?: string;
  userAgent: string;
  ipAddress: string;
}

// Validation schemas
const collaborationSessionSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  name: Joi.string().required().max(255),
  description: Joi.string().max(1000).optional(),
  maxParticipants: Joi.number().min(1).max(100).default(10),
  settings: Joi.object({
    allowAnonymous: Joi.boolean().default(false),
    requireApproval: Joi.boolean().default(false),
    enableVoiceChat: Joi.boolean().default(false),
    enableVideoChat: Joi.boolean().default(false),
    enableScreenShare: Joi.boolean().default(false),
    autoSave: Joi.boolean().default(true),
    conflictResolution: Joi.string().default('last_writer_wins')
  }).default({}),
  metadata: Joi.object().default({})
});

const messageSchema = Joi.object({
  channelId: Joi.string().uuid().optional(),
  sessionId: Joi.string().uuid().optional(),
  type: Joi.string().valid(...Object.values(MessageType)).default(MessageType.TEXT),
  content: Joi.string().required().max(10000),
  recipientIds: Joi.array().items(Joi.string().uuid()).min(1).required(),
  parentMessageId: Joi.string().uuid().optional(),
  threadId: Joi.string().uuid().optional(),
  attachments: Joi.array().items(Joi.object()).default([]),
  metadata: Joi.object().default({})
});

const pushNotificationSchema = Joi.object({
  organizationId: Joi.string().uuid().required(),
  notification: Joi.object({
    title: Joi.string().required().max(255),
    body: Joi.string().required().max(1000),
    icon: Joi.string().uri().optional(),
    image: Joi.string().uri().optional(),
    badge: Joi.number().min(0).optional(),
    sound: Joi.string().optional(),
    data: Joi.object().optional()
  }).required(),
  recipients: Joi.array().items(Joi.object({
    userId: Joi.string().uuid().required(),
    deviceTokens: Joi.array().items(Joi.string()).optional(),
    tags: Joi.array().items(Joi.string()).optional()
  })).min(1).required(),
  options: Joi.object({
    priority: Joi.string().valid(...Object.values(NotificationPriority)).default(NotificationPriority.NORMAL),
    ttl: Joi.number().min(0).default(86400),
    delayWhileIdle: Joi.boolean().default(false),
    collapseKey: Joi.string().optional(),
    dryRun: Joi.boolean().default(false)
  }).default({}),
  scheduling: Joi.object({
    sendAt: Joi.string().isoDate().required(),
    timezone: Joi.string().default('UTC'),
    recurring: Joi.object().optional()
  }).optional()
});

const emailSchema = Joi.object({
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  type: Joi.string().valid(...Object.values(EmailType)).required(),
  to: Joi.array().items(Joi.object({
    email: Joi.string().email().required(),
    name: Joi.string().optional()
  })).min(1).required(),
  cc: Joi.array().items(Joi.object()).optional(),
  bcc: Joi.array().items(Joi.object()).optional(),
  subject: Joi.string().required().max(255),
  content: Joi.object({
    html: Joi.string().optional(),
    text: Joi.string().optional(),
    amp: Joi.string().optional()
  }).required(),
  templateId: Joi.string().uuid().optional(),
  templateData: Joi.object().optional(),
  priority: Joi.string().valid(...Object.values(NotificationPriority)).default(NotificationPriority.NORMAL),
  attachments: Joi.array().items(Joi.object()).default([]),
  scheduling: Joi.object().optional(),
  tracking: Joi.object({
    trackOpens: Joi.boolean().default(true),
    trackClicks: Joi.boolean().default(true),
    trackUnsubscribes: Joi.boolean().default(true)
  }).default({}),
  metadata: Joi.object().default({})
});

/**
 * Unified Communication & Collaboration Manager
 * Handles all communication operations with comprehensive error handling, caching, and real-time processing
 */
class UnifiedCommunicationManager {

  private serviceBusService: ServiceBusEnhancedService;
  private signalRService: signalRService;

  constructor() {
    // Initialize Service Bus service for communication processing
    this.serviceBusService = ServiceBusEnhancedService.getInstance();

    // Initialize SignalR service for real-time communication
    this.signalRService = signalRService.getInstance();
  }

  /**
   * Create collaboration session
   */
  async createCollaborationSession(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;
    const startTime = Date.now();

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Validate request
      const body = await request.json();
      const { error, value } = collaborationSessionSchema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const sessionRequest = value;

      // Get document to verify access
      const document = await db.readItem('documents', sessionRequest.documentId, user.tenantId || 'default');
      if (!document) {
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Document not found' }
        }, request);
      }

      // Check permissions
      const hasPermission = await this.checkCollaborationPermission(
        (document as any).organizationId,
        user.id,
        'create_session'
      );
      if (!hasPermission) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'Access denied' }
        }, request);
      }

      // Create collaboration session
      const sessionId = uuidv4();
      const now = new Date().toISOString();

      const session: CollaborationSession = {
        id: sessionId,
        documentId: sessionRequest.documentId,
        organizationId: (document as any).organizationId,
        projectId: (document as any).projectId,
        name: sessionRequest.name,
        description: sessionRequest.description,
        status: SessionStatus.ACTIVE,
        participants: [{
          userId: user.id,
          role: 'owner',
          permissions: ['read', 'write', 'manage'],
          joinedAt: now,
          lastActivity: now,
          isActive: true
        }],
        maxParticipants: sessionRequest.maxParticipants,
        settings: sessionRequest.settings,
        metadata: sessionRequest.metadata,
        createdBy: user.id,
        createdAt: now,
        updatedAt: now,
        tenantId: user.tenantId || user.id
      };

      await db.createItem('collaboration-sessions', session);

      // Cache session for quick access
      await redis.setex(`session:${sessionId}`, 3600, JSON.stringify(session));

      // Create SignalR group for session
      await this.signalRService.addToGroup(user.id, `session-${sessionId}`);

      // Send to Service Bus for collaboration workflow
      await this.serviceBusService.sendToTopic('document-collaboration', {
        body: {
          sessionId,
          documentId: sessionRequest.documentId,
          action: 'session-created',
          createdBy: user.id,
          organizationId: (document as any).organizationId,
          projectId: (document as any).projectId,
          maxParticipants: sessionRequest.maxParticipants,
          timestamp: now
        },
        messageId: `collab-session-${sessionId}-${Date.now()}`,
        correlationId,
        subject: 'collaboration.session.created'
      });

      // Publish event
      await eventGridIntegration.publishEvent({
        eventType: 'Collaboration.SessionCreated',
        subject: `collaboration/sessions/${sessionId}/created`,
        data: {
          sessionId,
          documentId: sessionRequest.documentId,
          organizationId: (document as any).organizationId,
          createdBy: user.id,
          correlationId
        }
      });

      logger.info('Collaboration session created successfully', {
        correlationId,
        sessionId,
        documentId: sessionRequest.documentId,
        userId: user.id,
        processingTime: Date.now() - startTime
      });

      return addCorsHeaders({
        status: 201,
        jsonBody: {
          success: true,
          session: this.sanitizeSession(session),
          processingTime: Date.now() - startTime
        }
      }, request);

    } catch (error) {
      logger.error('Collaboration session creation failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Send message
   */
  async sendMessage(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;
    const startTime = Date.now();

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Validate request
      const body = await request.json();
      const { error, value } = messageSchema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const messageRequest = value;

      // Create message
      const messageId = uuidv4();
      const now = new Date().toISOString();

      const message: Message = {
        id: messageId,
        channelId: messageRequest.channelId,
        sessionId: messageRequest.sessionId,
        type: messageRequest.type,
        content: messageRequest.content,
        metadata: messageRequest.metadata,
        attachments: messageRequest.attachments,
        senderId: user.id,
        recipientIds: messageRequest.recipientIds,
        organizationId: user.organizationId || '',
        projectId: messageRequest.projectId,
        parentMessageId: messageRequest.parentMessageId,
        threadId: messageRequest.threadId,
        reactions: [],
        editHistory: [],
        deliveryStatus: DeliveryStatus.PENDING,
        readBy: [],
        createdAt: now,
        updatedAt: now,
        tenantId: user.tenantId || user.id
      };

      await db.createItem('messages', message);

      // Cache message for quick access
      await redis.setex(`message:${messageId}`, 1800, JSON.stringify(message));

      // Send real-time message via SignalR
      await this.broadcastMessage(message, correlationId);

      // Send to Service Bus for message processing
      await this.serviceBusService.sendToQueue('message-processing', {
        body: {
          messageId,
          message,
          action: 'message-sent',
          timestamp: now
        },
        correlationId,
        messageId: `msg-${messageId}-${Date.now()}`
      });

      // Update delivery status
      message.deliveryStatus = DeliveryStatus.SENT;
      await db.updateItem('messages', { ...message, id: messageId });

      logger.info('Message sent successfully', {
        correlationId,
        messageId,
        senderId: user.id,
        recipientCount: messageRequest.recipientIds.length,
        processingTime: Date.now() - startTime
      });

      return addCorsHeaders({
        status: 201,
        jsonBody: {
          success: true,
          messageId,
          status: DeliveryStatus.SENT,
          processingTime: Date.now() - startTime
        }
      }, request);

    } catch (error) {
      logger.error('Message sending failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Send push notification
   */
  async sendPushNotification(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;
    const startTime = Date.now();

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Validate request
      const body = await request.json();
      const { error, value } = pushNotificationSchema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const notificationRequest = value;

      // Check permissions
      const hasPermission = await this.checkCommunicationPermission(
        notificationRequest.organizationId,
        user.id,
        'send_push_notification'
      );
      if (!hasPermission) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'Access denied' }
        }, request);
      }

      // Create notification record
      const notificationId = uuidv4();
      const now = new Date().toISOString();

      const pushNotification: PushNotification = {
        id: notificationId,
        organizationId: notificationRequest.organizationId,
        notification: notificationRequest.notification,
        recipients: notificationRequest.recipients,
        options: notificationRequest.options,
        scheduling: notificationRequest.scheduling,
        status: DeliveryStatus.PENDING,
        deliveryStats: {
          totalRecipients: notificationRequest.recipients.length,
          sent: 0,
          delivered: 0,
          failed: 0,
          clicked: 0,
          dismissed: 0
        },
        createdBy: user.id,
        createdAt: now,
        tenantId: user.tenantId || user.id
      };

      await db.createItem('push-notifications', pushNotification);

      // Send to Service Bus for background processing
      const queueName = notificationRequest.scheduling ? 'scheduled-notifications' : 'immediate-notifications';

      await this.serviceBusService.sendToQueue(queueName, {
        body: {
          notificationId,
          notification: pushNotification,
          action: 'send-push-notification',
          timestamp: now
        },
        correlationId,
        messageId: `push-${notificationId}-${Date.now()}`,
        scheduledEnqueueTime: notificationRequest.scheduling?.sendAt ? new Date(notificationRequest.scheduling.sendAt) : undefined
      });

      // Cache notification for tracking
      await redis.setex(`notification:${notificationId}`, 86400, JSON.stringify(pushNotification));

      // Publish event
      await eventGridIntegration.publishEvent({
        eventType: 'Communication.PushNotificationQueued',
        subject: `notifications/push/${notificationId}/queued`,
        data: {
          notificationId,
          organizationId: notificationRequest.organizationId,
          recipientCount: notificationRequest.recipients.length,
          createdBy: user.id,
          correlationId
        }
      });

      logger.info('Push notification queued successfully', {
        correlationId,
        notificationId,
        organizationId: notificationRequest.organizationId,
        recipientCount: notificationRequest.recipients.length,
        userId: user.id,
        processingTime: Date.now() - startTime
      });

      return addCorsHeaders({
        status: 202,
        jsonBody: {
          success: true,
          notificationId,
          status: DeliveryStatus.PENDING,
          estimatedDelivery: this.estimateNotificationDelivery(notificationRequest),
          processingTime: Date.now() - startTime
        }
      }, request);

    } catch (error) {
      logger.error('Push notification failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Send email
   */
  async sendEmail(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;
    const startTime = Date.now();

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Validate request
      const body = await request.json();
      const { error, value } = emailSchema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const emailRequest = value;

      // Check permissions
      const hasPermission = await this.checkCommunicationPermission(
        emailRequest.organizationId,
        user.id,
        'send_email'
      );
      if (!hasPermission) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'Access denied' }
        }, request);
      }

      // Create email record
      const emailId = uuidv4();
      const now = new Date().toISOString();

      const emailMessage: EmailMessage = {
        id: emailId,
        organizationId: emailRequest.organizationId,
        projectId: emailRequest.projectId,
        type: emailRequest.type,
        from: {
          email: process.env.DEFAULT_FROM_EMAIL || '<EMAIL>',
          name: process.env.DEFAULT_FROM_NAME || 'System'
        },
        to: emailRequest.to,
        cc: emailRequest.cc,
        bcc: emailRequest.bcc,
        subject: emailRequest.subject,
        content: emailRequest.content,
        attachments: emailRequest.attachments || [],
        templateId: emailRequest.templateId,
        templateData: emailRequest.templateData,
        priority: emailRequest.priority,
        status: EmailStatus.QUEUED,
        scheduling: emailRequest.scheduling,
        tracking: {
          trackOpens: emailRequest.tracking?.trackOpens ?? true,
          trackClicks: emailRequest.tracking?.trackClicks ?? true,
          trackUnsubscribes: emailRequest.tracking?.trackUnsubscribes ?? true,
          opens: [],
          clicks: [],
          unsubscribes: []
        },
        metadata: emailRequest.metadata,
        createdBy: user.id,
        createdAt: now,
        tenantId: user.tenantId || user.id
      };

      await db.createItem('emails', emailMessage);

      // Send to Service Bus for email processing
      const queueName = emailRequest.scheduling ? 'scheduled-emails' : 'immediate-emails';

      await this.serviceBusService.sendToQueue(queueName, {
        body: {
          emailId,
          email: emailMessage,
          action: 'send-email',
          timestamp: now
        },
        correlationId,
        messageId: `email-${emailId}-${Date.now()}`,
        scheduledEnqueueTime: emailRequest.scheduling?.sendAt ? new Date(emailRequest.scheduling.sendAt) : undefined,
        timeToLive: 24 * 60 * 60 * 1000 // 24 hours TTL
      });

      // Cache email for tracking
      await redis.setex(`email:${emailId}`, 86400, JSON.stringify(emailMessage));

      // Publish event
      await eventGridIntegration.publishEvent({
        eventType: 'Communication.EmailQueued',
        subject: `emails/${emailId}/queued`,
        data: {
          emailId,
          organizationId: emailRequest.organizationId,
          type: emailRequest.type,
          recipientCount: emailRequest.to.length,
          createdBy: user.id,
          correlationId
        }
      });

      logger.info('Email queued successfully', {
        correlationId,
        emailId,
        organizationId: emailRequest.organizationId,
        type: emailRequest.type,
        recipientCount: emailRequest.to.length,
        userId: user.id,
        processingTime: Date.now() - startTime
      });

      return addCorsHeaders({
        status: 202,
        jsonBody: {
          success: true,
          emailId,
          status: EmailStatus.QUEUED,
          estimatedDelivery: this.estimateEmailDelivery(emailRequest),
          processingTime: Date.now() - startTime
        }
      }, request);

    } catch (error) {
      logger.error('Email sending failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Helper Methods with Full Production Integrations
   */
  private async checkCollaborationPermission(organizationId: string, userId: string, permission: string): Promise<boolean> {
    try {
      // Check Redis cache first for performance
      const cacheKey = `collab-permissions:${userId}:${organizationId}:${permission}`;
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Check organization membership and collaboration permissions
      const memberships = await db.queryItems<any>('organization-members',
        'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = "active"',
        [
          { name: '@orgId', value: organizationId },
          { name: '@userId', value: userId }
        ]
      );

      if (memberships.length === 0) {
        await redis.setex(cacheKey, 300, JSON.stringify(false));
        return false;
      }

      const membership = memberships[0];

      // Admin and Owner have all collaboration permissions
      const hasPermission = membership.role === 'ADMIN' ||
                           membership.role === 'OWNER' ||
                           membership.permissions?.includes(`collaboration_${permission}`) ||
                           membership.permissions?.includes('collaboration_all') || false;

      // Cache result for 5 minutes
      await redis.setex(cacheKey, 300, JSON.stringify(hasPermission));

      return hasPermission;
    } catch (error) {
      logger.error('Error checking collaboration permission', {
        organizationId,
        userId,
        permission,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  private async checkCommunicationPermission(organizationId: string, userId: string, permission: string): Promise<boolean> {
    try {
      // Check Redis cache first for performance
      const cacheKey = `comm-permissions:${userId}:${organizationId}:${permission}`;
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Check organization membership and communication permissions
      const memberships = await db.queryItems<any>('organization-members',
        'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = "active"',
        [
          { name: '@orgId', value: organizationId },
          { name: '@userId', value: userId }
        ]
      );

      if (memberships.length === 0) {
        await redis.setex(cacheKey, 300, JSON.stringify(false));
        return false;
      }

      const membership = memberships[0];

      // Admin and Owner have all communication permissions
      const hasPermission = membership.role === 'ADMIN' ||
                           membership.role === 'OWNER' ||
                           membership.permissions?.includes(`communication_${permission}`) ||
                           membership.permissions?.includes('communication_all') || false;

      // Cache result for 5 minutes
      await redis.setex(cacheKey, 300, JSON.stringify(hasPermission));

      return hasPermission;
    } catch (error) {
      logger.error('Error checking communication permission', {
        organizationId,
        userId,
        permission,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  private async broadcastMessage(message: Message, correlationId: string): Promise<void> {
    try {
      // Broadcast to session if sessionId is provided
      if (message.sessionId) {
        await this.signalRService.sendToGroup(`session-${message.sessionId}`, {
          target: 'messageReceived',
          arguments: [this.sanitizeMessage(message)]
        });
      }

      // Broadcast to channel if channelId is provided
      if (message.channelId) {
        await this.signalRService.sendToGroup(`channel-${message.channelId}`, {
          target: 'messageReceived',
          arguments: [this.sanitizeMessage(message)]
        });
      }

      // Send to specific recipients
      for (const recipientId of message.recipientIds) {
        await this.signalRService.sendToUser(recipientId, {
          target: 'messageReceived',
          arguments: [this.sanitizeMessage(message)]
        });
      }

      // Cache message in Redis for real-time access
      const messageKey = `recent-messages:${message.organizationId}:${new Date().toISOString().split('T')[0]}`;
      await redis.lpush(messageKey, JSON.stringify(message));
      await redis.ltrim(messageKey, 0, 999); // Keep last 1000 messages
      await redis.expire(messageKey, 86400); // Expire after 24 hours

    } catch (error) {
      logger.error('Error broadcasting message', {
        messageId: message.id,
        correlationId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private estimateNotificationDelivery(notificationRequest: any): string {
    let baseTime = 30; // 30 seconds base

    // Adjust based on priority
    if (notificationRequest.options.priority === NotificationPriority.URGENT) {
      baseTime = 10; // 10 seconds for urgent
    } else if (notificationRequest.options.priority === NotificationPriority.LOW) {
      baseTime = 120; // 2 minutes for low priority
    }

    // Adjust based on recipient count
    const recipientMultiplier = Math.ceil(notificationRequest.recipients.length / 100);
    baseTime *= recipientMultiplier;

    const deliveryTime = new Date(Date.now() + baseTime * 1000);
    return deliveryTime.toISOString();
  }

  private estimateEmailDelivery(emailRequest: any): string {
    let baseTime = 60; // 1 minute base

    // Adjust based on priority
    if (emailRequest.priority === NotificationPriority.URGENT) {
      baseTime = 30; // 30 seconds for urgent
    } else if (emailRequest.priority === NotificationPriority.LOW) {
      baseTime = 300; // 5 minutes for low priority
    }

    // Adjust based on recipient count
    const recipientMultiplier = Math.ceil(emailRequest.to.length / 50);
    baseTime *= recipientMultiplier;

    // Adjust for attachments
    if (emailRequest.attachments && emailRequest.attachments.length > 0) {
      baseTime += 30; // Add 30 seconds for attachments
    }

    const deliveryTime = new Date(Date.now() + baseTime * 1000);
    return deliveryTime.toISOString();
  }

  private sanitizeSession(session: CollaborationSession): any {
    const sanitized = { ...session };
    delete (sanitized as any)._rid;
    delete (sanitized as any)._self;
    delete (sanitized as any)._etag;
    delete (sanitized as any)._attachments;
    delete (sanitized as any)._ts;
    return sanitized;
  }

  private sanitizeMessage(message: Message): any {
    const sanitized = { ...message };
    delete (sanitized as any)._rid;
    delete (sanitized as any)._self;
    delete (sanitized as any)._etag;
    delete (sanitized as any)._attachments;
    delete (sanitized as any)._ts;
    return sanitized;
  }
}

// Create instance of the manager
const communicationManager = new UnifiedCommunicationManager();

/**
 * Additional Communication Functions
 */

/**
 * Join collaboration session
 */
async function joinCollaborationSession(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  const sessionId = request.url.split('/')[4]; // Extract from URL path

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const user = authResult.user;

    // Get session
    const session = await db.readItem('collaboration-sessions', sessionId, user.tenantId || 'default');
    if (!session) {
      return addCorsHeaders({
        status: 404,
        jsonBody: { error: 'Session not found' }
      }, request);
    }

    // Check if user is already a participant
    const existingParticipant = (session as any).participants.find((p: any) => p.userId === user.id);
    if (existingParticipant) {
      // Update last activity
      existingParticipant.lastActivity = new Date().toISOString();
      existingParticipant.isActive = true;
    } else {
      // Add new participant
      (session as any).participants.push({
        userId: user.id,
        role: 'participant',
        permissions: ['read', 'write'],
        joinedAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        isActive: true
      });
    }

    // Update session
    await db.updateItem('collaboration-sessions', { ...(session as any), id: sessionId });

    // Add user to SignalR group
    await communicationManager['signalRService'].addToGroup(user.id, `session-${sessionId}`);

    // Broadcast join event
    await communicationManager['signalRService'].sendToGroup(`session-${sessionId}`, {
      target: 'participantJoined',
      arguments: [{
        userId: user.id,
        timestamp: new Date().toISOString()
      }]
    });

    return addCorsHeaders({
      status: 200,
      jsonBody: {
        success: true,
        session: communicationManager['sanitizeSession'](session as any),
        joined: true
      }
    }, request);

  } catch (error) {
    logger.error('Session join failed', {
      error: error instanceof Error ? error.message : String(error),
      correlationId,
      sessionId
    });

    return addCorsHeaders({
      status: 500,
      jsonBody: { error: 'Internal server error' }
    }, request);
  }
}

// SignalR negotiate function
async function signalRNegotiate(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  try {
    // Get user context for authenticated connections
    let userId = 'anonymous';
    try {
      const authResult = await authenticateRequest(request);
      if (authResult.success && authResult.user) {
        userId = authResult.user.id;
      }
    } catch (error) {
      // Continue with anonymous connection
      logger.warn('SignalR negotiate: Authentication failed, using anonymous connection', {
        error: error instanceof Error ? error.message : String(error)
      });
    }

    // For Azure SignalR Service, return connection info
    const signalREndpoint = process.env.AZURE_SIGNALR_ENDPOINT || 'https://hepztech.service.signalr.net';
    const hubName = process.env.SIGNALR_HUB_NAME || 'hepztech';

    // In production with Azure SignalR Service, we would use the SignalR SDK
    // For now, return a basic connection info that works with the service
    const connectionInfo = {
      url: `${signalREndpoint}/client/?hub=${hubName}`,
      accessToken: '', // Azure SignalR Service will handle authentication
      availableTransports: [
        {
          transport: 'WebSockets',
          transferFormats: ['Text', 'Binary']
        },
        {
          transport: 'ServerSentEvents',
          transferFormats: ['Text']
        },
        {
          transport: 'LongPolling',
          transferFormats: ['Text', 'Binary']
        }
      ]
    };

    logger.info('SignalR negotiate successful', {
      userId,
      endpoint: signalREndpoint,
      hubName
    });

    return addCorsHeaders({
      status: 200,
      jsonBody: connectionInfo
    }, request);

  } catch (error) {
    logger.error('SignalR negotiate failed', {
      error: error instanceof Error ? error.message : String(error)
    });

    return addCorsHeaders({
      status: 500,
      jsonBody: { error: 'Internal server error' }
    }, request);
  }
}

// Register HTTP functions
app.http('signalr-negotiate', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'anonymous',
  route: 'signalr/negotiate',
  handler: signalRNegotiate
});

app.http('collaboration-session-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'collaboration/sessions',
  handler: (request, context) => communicationManager.createCollaborationSession(request, context)
});

app.http('collaboration-session-join', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'collaboration/sessions/{sessionId}/join',
  handler: joinCollaborationSession
});

app.http('message-send', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'communication/messages',
  handler: (request, context) => communicationManager.sendMessage(request, context)
});

app.http('push-notification-send', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'communication/notifications/push',
  handler: (request, context) => communicationManager.sendPushNotification(request, context)
});

app.http('email-send', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'communication/emails',
  handler: (request, context) => communicationManager.sendEmail(request, context)
});
